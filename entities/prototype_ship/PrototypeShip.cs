using Godot;
using System;

public partial class PrototypeShip : CharacterBody3D
{
    enum DirectionState
    {
        Forward,
        Back,
        Left,
        Right
    }

    DirectionState Direction { get; set; } = DirectionState.Forward;

    AnimationPlayer AnimationSystem { get; set; }

    double TimeSinceRightTurnInput { get; set; } = -1;
    double TimeSinceLeftTurnInput { get; set; } = -1;
    double TimeSinceTurnDone { get; set; } = -1;
    double TimeSinceThrustInput { get; set; } = -1;
    double TimeSinceThrustDone { get; set; } = -1;
    double TimeSinceLastInput { get; set; } = -1;
    double PerfectThreshold { get; set; } = 0.05;

    AudioStreamPlayer3D PerfectTurnSoundEffect { get; set; }
    AudioStreamPlayer3D TurnSoundEffect { get; set; }
    AudioStreamPlayer3D PerfectThrustSoundEffect { get; set; }
    AudioStreamPlayer3D ThrustSoundEffect { get; set; }

    public override void _Ready()
    {
        PerfectTurnSoundEffect = GetNode<AudioStreamPlayer3D>("SoundEffect - Perfect Turn");
        TurnSoundEffect = GetNode<AudioStreamPlayer3D>("SoundEffect - Turn");
        PerfectThrustSoundEffect = GetNode<AudioStreamPlayer3D>("SoundEffect - Perfect Thrust");
        ThrustSoundEffect = GetNode<AudioStreamPlayer3D>("SoundEffect - Thrust");
        AnimationSystem = GetNode<AnimationPlayer>("AnimationPlayer");
        AnimationSystem.AnimationFinished += OnAnimationFinished;
    }

    public override void _Process(double delta)
    {
        PollForInput();
        UpdateTimers(delta);
        MoveAndSlide();
    }

    private void PollForInput()
    {
        if (InputSystem.CurrentInputState != InputSystem.InputState.Entity)
        {
            return;
        }

        bool RotateLeftJustPressed = Input.IsActionJustPressed("Rotate-Left");
        bool RotateRightJustPressed = Input.IsActionJustPressed("Rotate-Right");
        bool ThrustJustPressed = Input.IsActionJustPressed("Thrust-Forward");

        if (RotateLeftJustPressed || TimeSinceLeftTurnInput >= 0)
        {
            if (RotateLeftJustPressed
                && AnimationSystem.IsPlaying()
                && AnimationSystem.CurrentAnimation.Contains("prototype_ship")
            )
            {
                TimeSinceLeftTurnInput = 0;
                TimeSinceLastInput = 0;
            }
            if (!AnimationSystem.IsPlaying())
            {
                RotateLeft();
            }
        }

        if (RotateRightJustPressed || TimeSinceRightTurnInput >= 0)
        {
            if (RotateRightJustPressed
                && AnimationSystem.IsPlaying()
                && AnimationSystem.CurrentAnimation.Contains("prototype_ship")
            )
            {
                TimeSinceRightTurnInput = 0;
                TimeSinceLastInput = 0;
            }
            if (!AnimationSystem.IsPlaying())
            {
                RotateRight();
            }
        }

        if ((ThrustJustPressed || TimeSinceThrustInput >= 0) && !(RotateLeftJustPressed || RotateRightJustPressed))
        {
            if (ThrustJustPressed
                && AnimationSystem.IsPlaying()
                && AnimationSystem.CurrentAnimation.Contains("prototype_ship")
            )
            {
                TimeSinceThrustInput = 0;
                TimeSinceLastInput = 0;
            }
            if (!AnimationSystem.IsPlaying())
            {
                Thrust();
            }
        }
    }

    private void UpdateTimers(double delta)
    {
        if (TimeSinceLeftTurnInput >= 0)
        {
            TimeSinceLeftTurnInput += delta;
            if (TimeSinceLeftTurnInput > 2 * PerfectThreshold)
            {
                TimeSinceLeftTurnInput = -1;
            }
        }

        if (TimeSinceRightTurnInput >= 0)
        {
            TimeSinceRightTurnInput += delta;
            if (TimeSinceRightTurnInput > 2 * PerfectThreshold)
            {
                TimeSinceRightTurnInput = -1;
            }
        }

        if (TimeSinceTurnDone >= 0)
        {
            TimeSinceTurnDone += delta;
            if (TimeSinceTurnDone > 2 * PerfectThreshold)
            {
                TimeSinceTurnDone = -1;
            }
        }

        if (TimeSinceThrustInput >= 0)
        {
            TimeSinceThrustInput += delta;
            if (TimeSinceThrustInput > 2 * PerfectThreshold)
            {
                TimeSinceThrustInput = -1;
            }
        }

        if (TimeSinceThrustDone >= 0)
        {
            TimeSinceThrustDone += delta;
            if (TimeSinceThrustDone > 2 * PerfectThreshold)
            {
                TimeSinceThrustDone = -1;
            }
        }

        if (TimeSinceLastInput >= 0)
        {
            TimeSinceLastInput += delta;
            if (TimeSinceLastInput > 2 * PerfectThreshold)
            {
                TimeSinceLastInput = -1;
            }
        }
    }

    public void Thrust()
    {
        int speed = 40;
        Velocity = Direction switch
        {
            DirectionState.Forward => new Vector3(0, 0, -speed),
            DirectionState.Left => new Vector3(-speed, 0, 0),
            DirectionState.Back => new Vector3(0, 0, speed),
            DirectionState.Right => new Vector3(speed, 0, 0),
            _ => new Vector3(0, 0, 0)
        };

        bool IsPerfectThrust = (TimeSinceLastInput >= 0 && TimeSinceLastInput <= PerfectThreshold)
            || (TimeSinceThrustDone >= 0 && TimeSinceThrustDone <= PerfectThreshold);

        if (IsPerfectThrust)
        {
            PerfectThrustSoundEffect.Play();
        }
        else
        {
            ThrustSoundEffect.Play();
        }

        AnimationSystem.Play("prototype_ship--thrust_forward_discrete");
    }

    public void RotateLeft()
    {

        DirectionState NextDirection = Direction switch
        {
            DirectionState.Forward => DirectionState.Left,
            DirectionState.Left => DirectionState.Back,
            DirectionState.Back => DirectionState.Right,
            DirectionState.Right => DirectionState.Forward,
            _ => DirectionState.Forward
        };

        bool IsPerfectTurn = (TimeSinceLastInput >= 0 && TimeSinceLastInput <= PerfectThreshold)
            || (TimeSinceTurnDone >= 0 && TimeSinceTurnDone <= PerfectThreshold);

        if (IsPerfectTurn)
        {
            PerfectTurnSoundEffect.Play();
        }
        else
        {
            TurnSoundEffect.Play();
        }

        switch ((Direction, NextDirection))
        {
            case (DirectionState.Forward, DirectionState.Left):
                AnimationSystem.Play("prototype_ship--rotate_forward_to_left_discrete");
                break;
            case (DirectionState.Left, DirectionState.Back):
                AnimationSystem.Play("prototype_ship--rotate_left_to_back_discrete");
                break;
            case (DirectionState.Back, DirectionState.Right):
                AnimationSystem.Play("prototype_ship--rotate_back_to_right_discrete");
                break;
            case (DirectionState.Right, DirectionState.Forward):
                AnimationSystem.Play("prototype_ship--rotate_right_to_forward_discrete");
                break;
            default:
                break;
        }
        ;

        Direction = NextDirection;
    }

    public void RotateRight()
    {
        if (AnimationSystem.IsPlaying())
        {
            return;
        }

        DirectionState NextDirection = Direction switch
        {
            DirectionState.Forward => DirectionState.Right,
            DirectionState.Right => DirectionState.Back,
            DirectionState.Back => DirectionState.Left,
            DirectionState.Left => DirectionState.Forward,
            _ => DirectionState.Forward
        };

        bool IsPerfectTurn = (TimeSinceLastInput >= 0 && TimeSinceLastInput <= PerfectThreshold)
            || (TimeSinceTurnDone >= 0 && TimeSinceTurnDone <= PerfectThreshold);

        if (IsPerfectTurn)
        {
            PerfectTurnSoundEffect.Play();
        }
        else
        {
            TurnSoundEffect.Play();
        }

        switch ((Direction, NextDirection))
        {
            case (DirectionState.Forward, DirectionState.Right):
                AnimationSystem.Play("prototype_ship--rotate_forward_to_right_discrete");
                break;
            case (DirectionState.Right, DirectionState.Back):
                AnimationSystem.Play("prototype_ship--rotate_right_to_back_discrete");
                break;
            case (DirectionState.Back, DirectionState.Left):
                AnimationSystem.Play("prototype_ship--rotate_back_to_left_discrete");
                break;
            case (DirectionState.Left, DirectionState.Forward):
                AnimationSystem.Play("prototype_ship--rotate_left_to_forward_discrete");
                break;
            default:
                break;
        }
        ;

        Direction = NextDirection;
    }

    private void OnAnimationFinished(Godot.StringName animationName)
    {
        if (animationName.ToString().Contains("prototype_ship--rotate"))
        {
            TimeSinceTurnDone = 0;
        }

        if (animationName.ToString().Contains("prototype_ship--thrust"))
        {
            Velocity = new Vector3(0, 0, 0);
            TimeSinceThrustDone = 0;
        }
    }
}
