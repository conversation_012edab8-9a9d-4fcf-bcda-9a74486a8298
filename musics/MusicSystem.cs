using Godot;
using System.Collections.Generic;

public partial class MusicSystem : Node
{
    List<AudioStreamPlayer> MusicTracks { get; set; } = new List<AudioStreamPlayer>();

    int currentTrackIndex = 0;

    public override void _Ready()
    {
        foreach (Node child in GetChildren())
        {
            if (child is AudioStreamPlayer track)
            {
                MusicTracks.Add(track);

                track.Finished += () =>
                {
                    currentTrackIndex = (currentTrackIndex + 1) % MusicTracks.Count;
                    MusicTracks[currentTrackIndex].Play();
                };

            }
        }
    }

}
